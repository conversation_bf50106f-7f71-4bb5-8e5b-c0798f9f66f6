<?php
/**
 * إنشاء جداول قاعدة البيانات الجديدة
 * Create new database tables
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h2>إنشاء جداول قاعدة البيانات الجديدة</h2>";

try {
    // تضمين إعدادات قاعدة البيانات الجديدة
    require_once 'config/database-new.php';
    
    echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    echo "<h3>2. إنشاء الجداول:</h3>";
    
    // قراءة ملف SQL
    $sql_file = 'database_design.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف قاعدة البيانات غير موجود: " . $sql_file);
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // تنظيف المحتوى وإزالة التعليقات
    $sql_content = preg_replace('/--.*$/m', '', $sql_content);
    $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content);
    
    // إزالة أوامر إنشاء قاعدة البيانات واستخدامها
    $sql_content = preg_replace('/CREATE DATABASE.*?;/i', '', $sql_content);
    $sql_content = preg_replace('/USE.*?;/i', '', $sql_content);
    
    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sql_content)));
    
    $created_tables = 0;
    $errors = [];
    
    echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    
    foreach ($queries as $query) {
        if (!empty($query) && strlen(trim($query)) > 10) {
            try {
                $pdo->exec($query);
                
                // استخراج اسم الجدول من الاستعلام
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $query, $matches)) {
                    $table_name = $matches[1];
                    echo "<p style='color: green;'>✅ تم إنشاء الجدول: " . $table_name . "</p>";
                    $created_tables++;
                } else {
                    echo "<p style='color: blue;'>ℹ️ تم تنفيذ استعلام آخر</p>";
                }
                
            } catch (PDOException $e) {
                $error_msg = $e->getMessage();
                
                // تجاهل أخطاء الجداول الموجودة مسبقاً
                if (strpos($error_msg, 'already exists') !== false || strpos($error_msg, 'Table') !== false) {
                    if (preg_match('/CREATE TABLE\s+(\w+)/i', $query, $matches)) {
                        $table_name = $matches[1];
                        echo "<p style='color: orange;'>⚠️ الجدول موجود مسبقاً: " . $table_name . "</p>";
                    }
                } else {
                    $errors[] = $error_msg;
                    echo "<p style='color: red;'>❌ خطأ: " . $error_msg . "</p>";
                }
            }
        }
    }
    
    echo "</div>";
    
    echo "<h3>3. النتائج:</h3>";
    echo "<p><strong>الجداول المنشأة:</strong> " . $created_tables . "</p>";
    
    if (!empty($errors)) {
        echo "<p><strong>الأخطاء:</strong> " . count($errors) . "</p>";
        echo "<details><summary>عرض الأخطاء</summary>";
        foreach ($errors as $error) {
            echo "<p style='color: red; font-size: 12px;'>" . htmlspecialchars($error) . "</p>";
        }
        echo "</details>";
    }
    
    // فحص الجداول النهائي
    echo "<h3>4. فحص الجداول النهائي:</h3>";
    $tables_result = $database->checkTablesExist();
    
    if ($tables_result['all_exist']) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px;'>";
        echo "🎉 <strong>تم إنشاء جميع الجداول بنجاح!</strong><br>";
        echo "الجداول الموجودة: " . $tables_result['total_existing'] . " من " . $tables_result['total_required'];
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px;'>";
        echo "⚠️ <strong>بعض الجداول ما زالت مفقودة</strong><br>";
        echo "الجداول الموجودة: " . $tables_result['total_existing'] . " من " . $tables_result['total_required'] . "<br>";
        
        if (!empty($tables_result['missing'])) {
            echo "<strong>الجداول المفقودة:</strong><br>";
            foreach ($tables_result['missing'] as $missing_table) {
                echo "• " . $missing_table . "<br>";
            }
        }
        echo "</div>";
    }
    
    // عرض الجداول الموجودة
    if (!empty($tables_result['existing'])) {
        echo "<h4>الجداول الموجودة:</h4>";
        echo "<div style='columns: 3; column-gap: 20px;'>";
        foreach ($tables_result['existing'] as $table) {
            echo "<p style='margin: 2px 0; color: green;'>✅ " . $table . "</p>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px;'>";
    echo "❌ <strong>خطأ:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li><a href='fix-api-files.php'>إصلاح ملفات API لتستخدم قاعدة البيانات الجديدة</a></li>";
echo "<li><a href='final-update-status.php'>مراجعة التقرير النهائي</a></li>";
echo "<li>اختبار وظائف الموقع</li>";
echo "</ol>";
?>
