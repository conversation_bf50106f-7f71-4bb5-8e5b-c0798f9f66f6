<?php
/**
 * اختبار الاتصال بقاعدة البيانات الجديدة
 * Test New Database Connection
 */

header('Content-Type: application/json; charset=utf-8');

// تضمين ملف الإعدادات الجديد
require_once 'config/database-new.php';

try {
    echo "<h2>اختبار الاتصال بقاعدة البيانات الجديدة</h2>";
    
    // إنشاء كائن قاعدة البيانات
    $db = new Database();
    
    // اختبار الاتصال
    echo "<h3>1. اختبار الاتصال:</h3>";
    $connection_result = $db->testConnection();
    
    if ($connection_result['success']) {
        echo "<p style='color: green;'>✅ تم الاتصال بنجاح!</p>";
        echo "<ul>";
        echo "<li>الخادم: " . $connection_result['host'] . "</li>";
        echo "<li>قاعدة البيانات: " . $connection_result['database'] . "</li>";
        echo "<li>المستخدم: " . $connection_result['username'] . "</li>";
        echo "</ul>";
        
        // فحص الجداول
        echo "<h3>2. فحص الجداول:</h3>";
        $tables_result = $db->checkTablesExist();
        
        if ($tables_result['all_exist']) {
            echo "<p style='color: green;'>✅ جميع الجداول موجودة (" . $tables_result['total_existing'] . " جدول)</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ بعض الجداول مفقودة</p>";
            echo "<p>الجداول الموجودة: " . $tables_result['total_existing'] . " من " . $tables_result['total_required'] . "</p>";
            
            if (!empty($tables_result['missing'])) {
                echo "<p>الجداول المفقودة:</p>";
                echo "<ul>";
                foreach ($tables_result['missing'] as $table) {
                    echo "<li style='color: red;'>" . $table . "</li>";
                }
                echo "</ul>";
                
                echo "<h3>3. إنشاء الجداول المفقودة:</h3>";
                $create_result = $db->createDatabase();
                
                if ($create_result['success']) {
                    echo "<p style='color: green;'>✅ تم إنشاء الجداول بنجاح!</p>";
                    echo "<p>تم تنفيذ " . $create_result['queries_executed'] . " استعلام</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في إنشاء الجداول: " . $create_result['error'] . "</p>";
                }
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في الاتصال: " . $connection_result['message'] . "</p>";
        echo "<ul>";
        echo "<li>الخادم: " . $connection_result['host'] . "</li>";
        echo "<li>قاعدة البيانات: " . $connection_result['database'] . "</li>";
        echo "<li>المستخدم: " . $connection_result['username'] . "</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>إذا نجح الاتصال، يمكنك تحديث ملفات API لاستخدام قاعدة البيانات الجديدة</li>";
echo "<li>إذا فشل الاتصال، تحقق من بيانات الاتصال في ملف config/database-new.php</li>";
echo "<li>تأكد من أن خادم MySQL يعمل وأن قاعدة البيانات موجودة</li>";
echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
echo "</ol>";
?>
