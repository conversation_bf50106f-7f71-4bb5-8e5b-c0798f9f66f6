<?php
/**
 * إصلاح ملفات API لتستخدم قاعدة البيانات الجديدة
 * Fix API files to use new database
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h2>إصلاح ملفات API لتستخدم قاعدة البيانات الجديدة</h2>";

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'children-api.php',
    'medicines-api.php',
    'user-management-api.php',
    'vaccination-api-simple.php',
    'vaccination-status-api.php',
    'user-api-simple.php'
];

$fixed_files = [];
$failed_files = [];

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        try {
            echo "<h3>معالجة الملف: " . $file . "</h3>";
            
            // قراءة محتوى الملف
            $content = file_get_contents($file);
            $original_content = $content;
            
            // البحث عن النمط الحالي
            if (strpos($content, "require_once 'config/database-live.php';") !== false) {
                
                // إنشاء نسخة احتياطية
                $backup_file = $file . '.backup-fix.' . date('Y-m-d-H-i-s');
                file_put_contents($backup_file, $content);
                
                // استبدال المرجع
                $content = str_replace(
                    "require_once 'config/database-live.php';",
                    "require_once 'config/database-new.php';",
                    $content
                );
                
                // البحث عن استخدام getDatabaseConnection() واستبداله
                if (strpos($content, 'getDatabaseConnection()') !== false) {
                    // استبدال استخدام الدالة القديمة بالطريقة الجديدة
                    $content = str_replace(
                        '$pdo = getDatabaseConnection();',
                        '$database = new Database(); $pdo = $database->getConnection();',
                        $content
                    );
                }
                
                // كتابة المحتوى الجديد
                file_put_contents($file, $content);
                
                $fixed_files[] = [
                    'file' => $file,
                    'backup' => $backup_file,
                    'changes' => 'تم تغيير database-live.php إلى database-new.php'
                ];
                
                echo "<p style='color: green;'>✅ تم إصلاح الملف بنجاح</p>";
                echo "<p>النسخة الاحتياطية: " . $backup_file . "</p>";
                
            } else {
                echo "<p style='color: blue;'>ℹ️ الملف لا يحتاج إصلاح أو تم إصلاحه مسبقاً</p>";
            }
            
        } catch (Exception $e) {
            $failed_files[] = [
                'file' => $file,
                'error' => $e->getMessage()
            ];
            echo "<p style='color: red;'>❌ فشل في إصلاح الملف: " . $e->getMessage() . "</p>";
        }
    } else {
        $failed_files[] = [
            'file' => $file,
            'error' => 'الملف غير موجود'
        ];
        echo "<h3>الملف غير موجود: " . $file . "</h3>";
    }
}

echo "<hr>";
echo "<h3>ملخص النتائج:</h3>";

if (!empty($fixed_files)) {
    echo "<h4>الملفات التي تم إصلاحها:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الملف</th><th>النسخة الاحتياطية</th><th>التغييرات</th></tr>";
    
    foreach ($fixed_files as $file_info) {
        echo "<tr>";
        echo "<td>" . $file_info['file'] . "</td>";
        echo "<td>" . $file_info['backup'] . "</td>";
        echo "<td>" . $file_info['changes'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

if (!empty($failed_files)) {
    echo "<h4>الملفات التي فشل إصلاحها:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الملف</th><th>السبب</th></tr>";
    
    foreach ($failed_files as $file_info) {
        echo "<tr>";
        echo "<td>" . $file_info['file'] . "</td>";
        echo "<td style='color: red;'>" . $file_info['error'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// اختبار سريع للملفات المصلحة
echo "<h3>اختبار سريع للملفات المصلحة:</h3>";

foreach ($fixed_files as $file_info) {
    $file = $file_info['file'];
    $content = file_get_contents($file);
    
    echo "<h4>" . $file . ":</h4>";
    
    if (strpos($content, "require_once 'config/database-new.php';") !== false) {
        echo "<p style='color: green;'>✅ يستخدم database-new.php</p>";
    } else {
        echo "<p style='color: red;'>❌ لا يستخدم database-new.php</p>";
    }
    
    if (strpos($content, "require_once 'config/database-live.php';") !== false) {
        echo "<p style='color: orange;'>⚠️ ما زال يحتوي على مرجع database-live.php</p>";
    }
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li><a href='final-update-status.php'>مراجعة التقرير النهائي المحدث</a></li>";
echo "<li>اختبار وظائف الموقع للتأكد من عملها</li>";
echo "<li>حذف النسخ الاحتياطية بعد التأكد من عمل كل شيء</li>";
echo "</ol>";

echo "<h3>ملفات الإدارة:</h3>";
echo "<ul>";
echo "<li><a href='create-database-tables.php'>إنشاء جداول قاعدة البيانات</a></li>";
echo "<li><a href='test-database-connection.php'>اختبار الاتصال</a></li>";
echo "<li><a href='restore-backup.php'>إدارة النسخ الاحتياطية</a></li>";
echo "</ul>";
?>
