<?php
/**
 * تقرير نهائي عن حالة تحديث جميع ملفات API
 * Final status report for API files update
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h2>تقرير نهائي - حالة تحديث ملفات API</h2>";

// قائمة جميع ملفات API
$api_files = [
    'children-api.php' => 'تم التحديث تلقائياً',
    'medicines-api.php' => 'تم التحديث تلقائياً', 
    'messages-api.php' => 'تم التحديث يدوياً',
    'monthly-planning-api.php' => 'لا يحتاج تحديث (لا يستخدم قاعدة البيانات)',
    'user-management-api.php' => 'تم التحديث تلقائياً',
    'vaccination-api-simple.php' => 'تم التحديث تلقائياً',
    'vaccination-status-api.php' => 'تم التحديث تلقائياً',
    'vaccines-api.php' => 'تم التحديث يدوياً',
    'user-api-simple.php' => 'تم التحديث تلقائياً'
];

echo "<h3>حالة جميع ملفات API:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الملف</th><th>الحالة</th><th>يستخدم قاعدة البيانات الجديدة</th><th>ملاحظات</th></tr>";

foreach ($api_files as $file => $status) {
    echo "<tr>";
    echo "<td>" . $file . "</td>";
    echo "<td>";
    
    if (file_exists($file)) {
        echo "<span style='color: green;'>✅ موجود</span>";
    } else {
        echo "<span style='color: red;'>❌ مفقود</span>";
    }
    
    echo "</td>";
    echo "<td>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        if (strpos($content, "require_once 'config/database-new.php';") !== false) {
            echo "<span style='color: green;'>✅ نعم</span>";
        } elseif (strpos($content, "require_once 'config/database-live.php';") !== false) {
            echo "<span style='color: orange;'>⚠️ يستخدم database-live.php</span>";
        } elseif (strpos($content, '$host = ') !== false && strpos($content, '$dbname = ') !== false) {
            echo "<span style='color: red;'>❌ يستخدم اتصال مباشر قديم</span>";
        } else {
            echo "<span style='color: blue;'>ℹ️ لا يستخدم قاعدة بيانات</span>";
        }
    } else {
        echo "<span style='color: gray;'>-</span>";
    }
    
    echo "</td>";
    echo "<td>" . $status . "</td>";
    echo "</tr>";
}

echo "</table>";

// فحص النسخ الاحتياطية
echo "<h3>النسخ الاحتياطية المتاحة:</h3>";
$backup_files = glob('*.backup.*');

if (!empty($backup_files)) {
    echo "<p>تم العثور على " . count($backup_files) . " نسخة احتياطية:</p>";
    echo "<ul>";
    foreach ($backup_files as $backup) {
        $file_time = filemtime($backup);
        echo "<li>" . $backup . " (تاريخ الإنشاء: " . date('Y-m-d H:i:s', $file_time) . ")</li>";
    }
    echo "</ul>";
    echo "<p><a href='restore-backup.php'>إدارة النسخ الاحتياطية</a></p>";
} else {
    echo "<p>لا توجد نسخ احتياطية.</p>";
}

// اختبار الاتصال
echo "<h3>اختبار الاتصال بقاعدة البيانات الجديدة:</h3>";

try {
    require_once 'config/database-new.php';
    $database = new Database();
    $connection_result = $database->testConnection();
    
    if ($connection_result['success']) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✅ الاتصال بقاعدة البيانات الجديدة ناجح!<br>";
        echo "الخادم: " . $connection_result['host'] . "<br>";
        echo "قاعدة البيانات: " . $connection_result['database'] . "<br>";
        echo "المستخدم: " . $connection_result['username'];
        echo "</div>";
        
        // فحص الجداول
        $tables_result = $database->checkTablesExist();
        if ($tables_result['all_exist']) {
            echo "<p style='color: green;'>✅ جميع الجداول المطلوبة موجودة (" . $tables_result['total_existing'] . " جدول)</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ بعض الجداول مفقودة (" . $tables_result['total_existing'] . " من " . $tables_result['total_required'] . ")</p>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "❌ فشل في الاتصال بقاعدة البيانات: " . $connection_result['message'];
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "❌ خطأ في اختبار الاتصال: " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<h3>الخطوات التالية الموصى بها:</h3>";
echo "<ol>";
echo "<li><strong>اختبار شامل:</strong> اختبر جميع وظائف الموقع للتأكد من عملها بشكل صحيح</li>";
echo "<li><strong>مراقبة الأخطاء:</strong> راقب ملفات السجل للتأكد من عدم وجود أخطاء</li>";
echo "<li><strong>نسخة احتياطية نهائية:</strong> أنشئ نسخة احتياطية كاملة من الموقع بعد التأكد من عمله</li>";
echo "<li><strong>حذف الملفات المؤقتة:</strong> احذف ملفات الاختبار والنسخ الاحتياطية القديمة بعد التأكد</li>";
echo "<li><strong>تحديث الوثائق:</strong> حدث أي وثائق تتعلق بإعدادات قاعدة البيانات</li>";
echo "</ol>";

echo "<h3>ملفات الاختبار والإدارة:</h3>";
echo "<ul>";
echo "<li><a href='test-database-connection.php'>اختبار الاتصال بقاعدة البيانات</a></li>";
echo "<li><a href='switch-to-new-database.php'>تحديث ملفات API</a></li>";
echo "<li><a href='restore-backup.php'>إدارة النسخ الاحتياطية</a></li>";
echo "<li><a href='final-update-status.php'>هذا التقرير</a></li>";
echo "</ul>";

echo "<p style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin-top: 20px;'>";
echo "<strong>ملاحظة:</strong> يمكنك حذف ملفات الاختبار والإدارة هذه بعد التأكد من عمل كل شيء بشكل صحيح.";
echo "</p>";
?>
