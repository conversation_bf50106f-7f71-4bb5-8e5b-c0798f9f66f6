<?php
/**
 * استعادة النسخ الاحتياطية من ملفات API
 * Restore API files from backup
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h2>استعادة النسخ الاحتياطية</h2>";

// البحث عن ملفات النسخ الاحتياطية
$backup_files = glob('*.backup.*');

if (empty($backup_files)) {
    echo "<p>لم يتم العثور على أي نسخ احتياطية.</p>";
    exit;
}

// تجميع النسخ الاحتياطية حسب الملف الأصلي
$backups_by_file = [];
foreach ($backup_files as $backup) {
    $original_file = preg_replace('/\.backup\.\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}$/', '', $backup);
    if (!isset($backups_by_file[$original_file])) {
        $backups_by_file[$original_file] = [];
    }
    $backups_by_file[$original_file][] = $backup;
}

// ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
foreach ($backups_by_file as $file => $backups) {
    rsort($backups_by_file[$file]);
}

if (isset($_POST['restore'])) {
    $file_to_restore = $_POST['backup_file'];
    $original_file = $_POST['original_file'];
    
    if (file_exists($file_to_restore)) {
        try {
            // إنشاء نسخة احتياطية من الملف الحالي قبل الاستعادة
            $current_backup = $original_file . '.current-backup.' . date('Y-m-d-H-i-s');
            if (file_exists($original_file)) {
                copy($original_file, $current_backup);
            }
            
            // استعادة النسخة الاحتياطية
            copy($file_to_restore, $original_file);
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "✅ تم استعادة الملف بنجاح!<br>";
            echo "الملف: " . $original_file . "<br>";
            echo "من النسخة الاحتياطية: " . $file_to_restore . "<br>";
            echo "تم إنشاء نسخة احتياطية من الملف الحالي: " . $current_backup;
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "❌ فشل في استعادة الملف: " . $e->getMessage();
            echo "</div>";
        }
    }
}

if (isset($_POST['delete_backup'])) {
    $backup_to_delete = $_POST['backup_to_delete'];
    
    if (file_exists($backup_to_delete)) {
        try {
            unlink($backup_to_delete);
            echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "🗑️ تم حذف النسخة الاحتياطية: " . $backup_to_delete;
            echo "</div>";
            
            // إعادة تحميل الصفحة لتحديث القائمة
            echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "❌ فشل في حذف النسخة الاحتياطية: " . $e->getMessage();
            echo "</div>";
        }
    }
}

echo "<h3>النسخ الاحتياطية المتاحة:</h3>";

foreach ($backups_by_file as $original_file => $backups) {
    echo "<div style='border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
    echo "<h4>الملف: " . $original_file . "</h4>";
    
    if (file_exists($original_file)) {
        echo "<p style='color: green;'>✅ الملف الأصلي موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ الملف الأصلي مفقود</p>";
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 10px;'>";
    echo "<tr><th>النسخة الاحتياطية</th><th>تاريخ الإنشاء</th><th>الحجم</th><th>الإجراءات</th></tr>";
    
    foreach ($backups as $backup) {
        $file_time = filemtime($backup);
        $file_size = filesize($backup);
        
        echo "<tr>";
        echo "<td>" . basename($backup) . "</td>";
        echo "<td>" . date('Y-m-d H:i:s', $file_time) . "</td>";
        echo "<td>" . number_format($file_size) . " بايت</td>";
        echo "<td>";
        
        // زر الاستعادة
        echo "<form method='post' style='display: inline; margin-right: 10px;'>";
        echo "<input type='hidden' name='backup_file' value='" . $backup . "'>";
        echo "<input type='hidden' name='original_file' value='" . $original_file . "'>";
        echo "<input type='submit' name='restore' value='استعادة' style='background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;' onclick='return confirm(\"هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\")'>";
        echo "</form>";
        
        // زر الحذف
        echo "<form method='post' style='display: inline;'>";
        echo "<input type='hidden' name='backup_to_delete' value='" . $backup . "'>";
        echo "<input type='submit' name='delete_backup' value='حذف' style='background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;' onclick='return confirm(\"هل أنت متأكد من حذف هذه النسخة الاحتياطية؟\")'>";
        echo "</form>";
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>عند استعادة نسخة احتياطية، سيتم إنشاء نسخة احتياطية من الملف الحالي تلقائياً</li>";
echo "<li>تأكد من اختبار الموقع بعد استعادة أي ملف</li>";
echo "<li>يمكنك حذف النسخ الاحتياطية القديمة لتوفير مساحة التخزين</li>";
echo "<li>احتفظ بنسخة احتياطية واحدة على الأقل من كل ملف</li>";
echo "</ul>";
?>
