<?php
/**
 * API إدارة اللقاحات والمخزون
 */

// تفعيل معالجة الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1); // تفعيل عرض الأخطاء للتشخيص
ini_set('log_errors', 1);

// تنظيف أي مخرجات سابقة
ob_clean();

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين إعدادات قاعدة البيانات الجديدة
require_once 'config/database-new.php';

try {
    // الاتصال بقاعدة البيانات الجديدة
    $database = new Database();
    $pdo = $database->getConnection();
    
    // إنشاء الجداول إذا لم تكن موجودة
    createVaccineTables($pdo);
    
    // قراءة البيانات
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (json_last_error() !== JSON_ERROR_NONE && !empty($rawInput)) {
        throw new Exception('خطأ في تحليل JSON: ' . json_last_error_msg());
    }

    if (!$input) {
        $input = $_POST;
    }

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }
    
    switch ($action) {
        case 'load_vaccines':
            loadVaccines($pdo, $input);
            break;
            
        case 'save_vaccine':
            saveVaccine($pdo, $input);
            break;
            
        case 'update_vaccine':
            updateVaccine($pdo, $input);
            break;
            
        case 'delete_vaccine':
            deleteVaccine($pdo, $input);
            break;
            
        case 'load_stock':
            loadVaccineStock($pdo, $input);
            break;
            
        case 'update_stock':
            updateVaccineStock($pdo, $input);
            break;
            
        case 'load_usage_log':
            loadUsageLog($pdo, $input);
            break;
            
        case 'add_usage':
            addUsageLog($pdo, $input);
            break;
            
        case 'load_monthly_planning':
            try {
                loadMonthlyPlanningAPI($pdo, $input);
            } catch (Exception $e) {
                error_log('خطأ في load_monthly_planning: ' . $e->getMessage());
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage(),
                    'file' => 'vaccines-api.php',
                    'action' => 'load_monthly_planning'
                ], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'save_monthly_planning':
            saveMonthlyPlanningAPI($pdo, $input);
            break;

        // دوال الأدوية تم نقلها إلى medicines-api.php

        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'API يعمل بشكل صحيح',
                'timestamp' => date('Y-m-d H:i:s'),
                'user_id' => $input['user_id'] ?? 'غير محدد'
            ], JSON_UNESCAPED_UNICODE);
            break;

        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات vaccines-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'vaccines-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(400);
    error_log('خطأ عام في vaccines-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_type' => 'general',
        'file' => 'vaccines-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

// إنشاء جداول اللقاحات
function createVaccineTables($pdo) {
    try {
        // جدول قائمة اللقاحات
        $sql1 = "
        CREATE TABLE IF NOT EXISTS vaccine_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            name VARCHAR(255) NOT NULL,
            quantity INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        // جدول مخزون اللقاحات
        $sql2 = "
        CREATE TABLE IF NOT EXISTS vaccine_stock (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            vaccine_key VARCHAR(100) NOT NULL,
            quantity INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_vaccine (user_id, vaccine_key),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        // جدول سجل استخدام اللقاحات
        $sql3 = "
        CREATE TABLE IF NOT EXISTS vaccine_usage_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            vaccine_key VARCHAR(100) NOT NULL,
            child_name VARCHAR(255),
            quantity_used INT DEFAULT 1,
            usage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id),
            INDEX idx_usage_date (usage_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        // جدول التخطيط الشهري للقاحات
        $sql4 = "
        CREATE TABLE IF NOT EXISTS vaccine_monthly_planning (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            month_key VARCHAR(20) NOT NULL,
            month_name VARCHAR(100),
            vaccines_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql1);
        $pdo->exec($sql2);
        $pdo->exec($sql3);
        $pdo->exec($sql4);

    } catch (Exception $e) {
        error_log('خطأ في إنشاء جداول اللقاحات: ' . $e->getMessage());
    }
}

// إنشاء جداول الأدوية
function createMedicineTables($pdo) {
    try {
        // جدول قائمة الأدوية
        $sql1 = "
        CREATE TABLE IF NOT EXISTS medicine_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            name VARCHAR(255) NOT NULL,
            unit VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // جدول التخطيط الشهري للأدوية
        $sql2 = "
        CREATE TABLE IF NOT EXISTS medicine_monthly_planning (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            month_key VARCHAR(20) NOT NULL,
            month_name VARCHAR(100),
            medicines_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // جدول مخزون الأدوية
        $sql3 = "
        CREATE TABLE IF NOT EXISTS medicine_stock (
            id INT AUTO_INCREMENT PRIMARY KEY,
            medicine_id VARCHAR(50),
            user_id VARCHAR(50),
            center_id VARCHAR(50),
            quantity INT DEFAULT 0,
            expiry_date DATE,
            batch_number VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_medicine_user (medicine_id, user_id),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        $pdo->exec($sql1);
        $pdo->exec($sql2);
        $pdo->exec($sql3);

    } catch (Exception $e) {
        error_log('خطأ في إنشاء جداول الأدوية: ' . $e->getMessage());
    }
}

// تحميل قائمة اللقاحات
function loadVaccines($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM vaccine_list 
            WHERE user_id = ? 
            ORDER BY name ASC
        ");
        
        $stmt->execute([$user_id]);
        $vaccines = $stmt->fetchAll();
        
        // إذا لم توجد لقاحات، إرجاع القائمة من جدول vaccines
        if (empty($vaccines)) {
            $defaultVaccines = getDefaultVaccines($pdo);
            echo json_encode([
                'success' => true,
                'vaccines' => $defaultVaccines,
                'is_default' => true
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'vaccines' => $vaccines,
            'is_default' => false
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            $defaultVaccines = getDefaultVaccines();
            echo json_encode([
                'success' => true,
                'vaccines' => $defaultVaccines,
                'is_default' => true,
                'message' => 'جدول اللقاحات غير موجود - سيتم إنشاؤه عند الحفظ'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        throw $e;
    }
}

// الحصول على قائمة اللقاحات من جدول vaccines
function getDefaultVaccines($pdo = null) {
    if ($pdo) {
        try {
            // جلب اللقاحات من جدول vaccines
            $stmt = $pdo->query("SELECT id, name_ar as name FROM vaccines WHERE is_active = 1 ORDER BY age_months ASC, name_ar ASC");
            $vaccines = $stmt->fetchAll();

            $result = [];
            foreach ($vaccines as $vaccine) {
                $result[] = [
                    'id' => $vaccine['id'],
                    'name' => $vaccine['name'],
                    'quantity' => 0
                ];
            }

            return $result;
        } catch (Exception $e) {
            error_log('خطأ في جلب اللقاحات من قاعدة البيانات: ' . $e->getMessage());
        }
    }

    // قائمة احتياطية تتطابق مع اللقاحات الموجودة في الصفحة
    return [
        ['id' => 'HB1', 'name' => 'التهاب الكبد ب', 'quantity' => 0],
        ['id' => 'BCG', 'name' => 'السل', 'quantity' => 0],
        ['id' => 'VPO', 'name' => 'شلل الأطفال الفموي', 'quantity' => 0],
        ['id' => 'VPI', 'name' => 'شلل الأطفال المحقون', 'quantity' => 0],
        ['id' => 'Rota', 'name' => 'الروتا', 'quantity' => 0],
        ['id' => 'Penta', 'name' => 'الخماسي', 'quantity' => 0],
        ['id' => 'RR', 'name' => 'الحصبة والحصبة الألمانية', 'quantity' => 0],
        ['id' => 'Pneumo', 'name' => 'المكورات الرئوية', 'quantity' => 0],
        ['id' => 'DTC', 'name' => 'الثلاثي', 'quantity' => 0],
        ['id' => 'VAT', 'name' => 'الكزاز للحوامل', 'quantity' => 0]
    ];
}

// حفظ لقاح جديد
function saveVaccine($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '';
    $vaccine_id = $input['id'] ?? '';
    $name = $input['name'] ?? '';
    $quantity = $input['quantity'] ?? 0;
    
    if (!$user_id || !$vaccine_id || !$name) {
        throw new Exception('البيانات الأساسية مطلوبة: user_id, id, name');
    }
    
    try {
        createVaccineTables($pdo);

        // فحص إذا كان الجدول موجود
        $stmt = $pdo->query("SHOW TABLES LIKE 'vaccine_list'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('جدول vaccine_list غير موجود');
        }

        // فحص الأعمدة الموجودة
        $stmt = $pdo->query("SHOW COLUMNS FROM vaccine_list");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');

        $insertColumns = ['id', 'user_id', 'name', 'quantity'];
        $insertValues = [$vaccine_id, $user_id, $name, $quantity];
        $placeholders = ['?', '?', '?', '?'];

        // إضافة center_id إذا كان العمود موجود
        if (in_array('center_id', $columnNames) && $center_id) {
            $insertColumns[] = 'center_id';
            $insertValues[] = $center_id;
            $placeholders[] = '?';
        }

        $updateParts = ['name = VALUES(name)', 'quantity = VALUES(quantity)'];
        if (in_array('updated_at', $columnNames)) {
            $updateParts[] = 'updated_at = CURRENT_TIMESTAMP';
        }

        $sql = "
            INSERT INTO vaccine_list (" . implode(', ', $insertColumns) . ")
            VALUES (" . implode(', ', $placeholders) . ")
            ON DUPLICATE KEY UPDATE " . implode(', ', $updateParts);

        $stmt = $pdo->prepare($sql);
        $stmt->execute($insertValues);

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ اللقاح بنجاح'
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        throw $e;
    }
}

// تحميل مخزون اللقاحات
function loadVaccineStock($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    try {
        // فحص الأعمدة الموجودة
        $stmt = $pdo->query("SHOW COLUMNS FROM vaccine_stock");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');

        // تحديد اسم العمود المناسب للقاح
        $vaccine_column = in_array('vaccine_key', $columnNames) ? 'vaccine_key' : 'vaccine_id';

        $stmt = $pdo->prepare("
            SELECT $vaccine_column as vaccine_key, quantity FROM vaccine_stock
            WHERE user_id = ?
        ");

        $stmt->execute([$user_id]);
        $stock = $stmt->fetchAll();

        // تحويل إلى تنسيق object
        $stockObject = [];
        foreach ($stock as $item) {
            $stockObject[$item['vaccine_key']] = (int)$item['quantity'];
        }
        
        echo json_encode([
            'success' => true,
            'stock' => $stockObject
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            echo json_encode([
                'success' => true,
                'stock' => [],
                'message' => 'جدول المخزون غير موجود - سيتم إنشاؤه عند التحديث'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        throw $e;
    }
}

// تحديث مخزون اللقاحات
function updateVaccineStock($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '';
    $stock_data = $input['stock'] ?? [];

    if (!$user_id || !is_array($stock_data)) {
        throw new Exception('معرف المستخدم وبيانات المخزون مطلوبان');
    }

    try {
        createVaccineTables($pdo);

        // فحص إذا كان الجدول موجود
        $stmt = $pdo->query("SHOW TABLES LIKE 'vaccine_stock'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('جدول vaccine_stock غير موجود');
        }

        // فحص الأعمدة الموجودة
        $stmt = $pdo->query("SHOW COLUMNS FROM vaccine_stock");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');

        $pdo->beginTransaction();

        foreach ($stock_data as $vaccine_key => $quantity) {
            // تحديد اسم العمود المناسب للقاح
            $vaccine_column = in_array('vaccine_key', $columnNames) ? 'vaccine_key' : 'vaccine_id';

            // تجاهل فحص وجود اللقاح لتجنب الأخطاء
            // if ($vaccine_column === 'vaccine_id') {
            //     ensureVaccineExists($pdo, $vaccine_key);
            // }

            $insertColumns = ['user_id', $vaccine_column, 'quantity'];
            $insertValues = [$user_id, $vaccine_key, (int)$quantity];
            $placeholders = ['?', '?', '?'];

            // إضافة center_id إذا كان العمود موجود
            if (in_array('center_id', $columnNames) && $center_id) {
                $insertColumns[] = 'center_id';
                $insertValues[] = $center_id;
                $placeholders[] = '?';
            }

            // محاولة تحديث السجل الموجود أولاً
            $updateSql = "UPDATE vaccine_stock SET quantity = ?";
            $updateValues = [(int)$quantity];
            $whereConditions = ["user_id = ?", "$vaccine_column = ?"];
            $updateValues[] = $user_id;
            $updateValues[] = $vaccine_key;

            if (in_array('updated_at', $columnNames)) {
                $updateSql .= ", updated_at = CURRENT_TIMESTAMP";
            }

            $updateSql .= " WHERE " . implode(' AND ', $whereConditions);

            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->execute($updateValues);

            // إذا لم يتم تحديث أي سجل، أدرج سجل جديد
            if ($updateStmt->rowCount() === 0) {
                $sql = "INSERT INTO vaccine_stock (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $placeholders) . ")";

                try {
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($insertValues);
                } catch (Exception $e) {
                    // إذا فشل الإدراج بسبب القيد الخارجي أو التكرار، تجاهل هذا اللقاح
                    if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                        error_log("تجاهل اللقاح $vaccine_key بسبب القيد الخارجي");
                        continue;
                    } elseif (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        error_log("تجاهل اللقاح $vaccine_key بسبب التكرار");
                        continue;
                    } else {
                        throw $e;
                    }
                }
            }
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث المخزون بنجاح'
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// ضمان وجود اللقاح في جدول vaccines
function ensureVaccineExists($pdo, $vaccine_id) {
    try {
        // فحص إذا كان جدول vaccines موجود
        $stmt = $pdo->query("SHOW TABLES LIKE 'vaccines'");
        if ($stmt->rowCount() === 0) {
            return; // الجدول غير موجود، تجاهل
        }

        // فحص إذا كان اللقاح موجود
        $stmt = $pdo->prepare("SELECT id FROM vaccines WHERE id = ?");
        $stmt->execute([$vaccine_id]);

        if ($stmt->rowCount() === 0) {
            // اللقاح غير موجود، أنشئه
            $vaccine_names = [
                'HB1' => 'التهاب الكبد ب',
                'BCG' => 'السل',
                'VPO' => 'شلل الأطفال الفموي',
                'VPI' => 'شلل الأطفال المحقون',
                'Rota' => 'الروتا',
                'Penta' => 'الخماسي',
                'RR' => 'الحصبة والحصبة الألمانية',
                'Pneumo' => 'المكورات الرئوية',
                'DTC' => 'الثلاثي',
                'VAT' => 'الكزاز للحوامل'
            ];

            $vaccine_name = $vaccine_names[$vaccine_id] ?? $vaccine_id;

            // فحص أعمدة جدول vaccines
            $stmt = $pdo->query("DESCRIBE vaccines");
            $columns = $stmt->fetchAll();
            $columnNames = array_column($columns, 'Field');

            $insertColumns = ['id'];
            $insertValues = [$vaccine_id];
            $placeholders = ['?'];

            // استخدام name_ar بدلاً من name
            if (in_array('name_ar', $columnNames)) {
                $insertColumns[] = 'name_ar';
                $insertValues[] = $vaccine_name;
                $placeholders[] = '?';
            } elseif (in_array('name', $columnNames)) {
                $insertColumns[] = 'name';
                $insertValues[] = $vaccine_name;
                $placeholders[] = '?';
            }

            if (in_array('name_fr', $columnNames)) {
                $insertColumns[] = 'name_fr';
                $insertValues[] = $vaccine_id; // استخدام المعرف كاسم فرنسي مؤقت
                $placeholders[] = '?';
            }

            if (in_array('is_active', $columnNames)) {
                $insertColumns[] = 'is_active';
                $insertValues[] = 1;
                $placeholders[] = '?';
            }

            $sql = "INSERT INTO vaccines (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $placeholders) . ")";

            try {
                $stmt = $pdo->prepare($sql);
                $stmt->execute($insertValues);
                error_log("تم إنشاء اللقاح: $vaccine_id - $vaccine_name");
            } catch (Exception $e) {
                error_log("خطأ في إنشاء اللقاح $vaccine_id: " . $e->getMessage());
            }
        }

    } catch (Exception $e) {
        error_log("خطأ في ضمان وجود اللقاح $vaccine_id: " . $e->getMessage());
    }
}

// تحميل التخطيط الشهري
function loadMonthlyPlanningAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';

    if (!$user_id) {
        returnDefaultPlanning();
        return;
    }

    try {
        // فحص إذا كان الجدول موجود
        $stmt = $pdo->query("SHOW TABLES LIKE 'vaccine_monthly_planning'");
        if ($stmt->rowCount() === 0) {
            returnDefaultPlanning();
            return;
        }

        // محاولة تحميل البيانات
        $stmt = $pdo->prepare("
            SELECT month_key, month_name, vaccines_data
            FROM vaccine_monthly_planning
            WHERE user_id = ?
            ORDER BY month_key ASC
        ");

        $stmt->execute([$user_id]);
        $planning = $stmt->fetchAll();

        // إعداد التخطيط الافتراضي
        $defaultVaccines = [
            'HB1' => 0, 'BCG' => 0, 'VPO' => 0, 'VPI' => 0, 'Rota' => 0,
            'Penta' => 0, 'RR' => 0, 'Pneumo' => 0, 'DTC' => 0, 'VAT' => 0
        ];

        $monthlyPlanning = [
            'month1' => ['name' => '', 'vaccines' => $defaultVaccines],
            'month2' => ['name' => '', 'vaccines' => $defaultVaccines],
            'month3' => ['name' => '', 'vaccines' => $defaultVaccines]
        ];

        // تحديث البيانات إذا وجدت
        foreach ($planning as $plan) {
            $monthKey = $plan['month_key'];
            if (isset($monthlyPlanning[$monthKey])) {
                $monthlyPlanning[$monthKey]['name'] = $plan['month_name'] ?? '';

                $vaccines_data = $plan['vaccines_data'] ?? '{}';
                $decoded = json_decode($vaccines_data, true);

                if (is_array($decoded)) {
                    // دمج البيانات المحفوظة مع الافتراضية
                    $monthlyPlanning[$monthKey]['vaccines'] = array_merge($defaultVaccines, $decoded);
                }
            }
        }

        echo json_encode([
            'success' => true,
            'planning' => $monthlyPlanning
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        error_log('خطأ في تحميل التخطيط الشهري: ' . $e->getMessage());
        returnDefaultPlanning();
    }
}

// إرجاع تخطيط افتراضي
function returnDefaultPlanning() {
    $defaultVaccines = [
        'HB1' => 0, 'BCG' => 0, 'VPO' => 0, 'VPI' => 0, 'Rota' => 0,
        'Penta' => 0, 'RR' => 0, 'Pneumo' => 0, 'DTC' => 0, 'VAT' => 0
    ];

    echo json_encode([
        'success' => true,
        'planning' => [
            'month1' => ['name' => '', 'vaccines' => $defaultVaccines],
            'month2' => ['name' => '', 'vaccines' => $defaultVaccines],
            'month3' => ['name' => '', 'vaccines' => $defaultVaccines]
        ],
        'is_default' => true
    ], JSON_UNESCAPED_UNICODE);
}

// حفظ التخطيط الشهري
function saveMonthlyPlanningAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '1';
    $planning = $input['planning'] ?? [];

    if (!$user_id || !is_array($planning)) {
        throw new Exception('معرف المستخدم وبيانات التخطيط مطلوبان');
    }

    try {
        createVaccineTables($pdo);

        $pdo->beginTransaction();

        foreach ($planning as $month_key => $month_data) {
            $month_name = $month_data['name'] ?? '';
            $vaccines_data = json_encode($month_data['vaccines'] ?? [], JSON_UNESCAPED_UNICODE);

            $stmt = $pdo->prepare("
                INSERT INTO vaccine_monthly_planning (user_id, center_id, month_key, month_name, vaccines_data)
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                month_name = VALUES(month_name),
                vaccines_data = VALUES(vaccines_data),
                updated_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([$user_id, $center_id, $month_key, $month_name, $vaccines_data]);
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ التخطيط الشهري في قاعدة البيانات بنجاح',
            'saved_months' => array_keys($planning),
            'user_id' => $user_id
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        $pdo->rollBack();
        error_log('خطأ في حفظ التخطيط الشهري: ' . $e->getMessage());
        throw $e;
    }
}

// تم نقل جميع دوال الأدوية إلى medicines-api.php

?>
