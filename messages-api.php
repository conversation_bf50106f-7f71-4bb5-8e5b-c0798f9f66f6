<?php
/**
 * API إدارة الرسائل
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين إعدادات قاعدة البيانات الجديدة
require_once 'config/database-new.php';

try {
    // الاتصال بقاعدة البيانات الجديدة
    $database = new Database();
    $pdo = $database->getConnection();
    
    // إنشاء جدول الرسائل إذا لم يكن موجوداً
    createMessagesTable($pdo);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $action = $input['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'load':
            loadMessages($pdo, $input);
            break;
            
        case 'send':
            sendMessage($pdo, $input);
            break;
            
        case 'mark_read':
            markMessagesAsRead($pdo, $input);
            break;
            
        case 'delete_conversation':
            deleteConversation($pdo, $input);
            break;
            
        case 'get_conversations':
            getConversations($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// إنشاء جدول الرسائل (الجدول موجود بالفعل)
function createMessagesTable($pdo) {
    // الجدول موجود بالفعل بالهيكل التالي:
    // - id (int, auto_increment, primary key)
    // - sender_id (varchar(50))
    // - receiver_id (varchar(50))
    // - message (text) - بدلاً من content
    // - attachments (json)
    // - is_read (tinyint(1))
    // - sent_at (timestamp) - بدلاً من created_at

    // لا حاجة لإنشاء الجدول
    return;
}

// تحميل الرسائل
function loadMessages($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $partner_id = $input['partner_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    if ($partner_id) {
        // تحميل رسائل محادثة محددة
        $stmt = $pdo->prepare("
            SELECT m.*,
                   s.name as sender_name,
                   r.name as receiver_name
            FROM messages m
            LEFT JOIN users s ON m.sender_id = s.id
            LEFT JOIN users r ON m.receiver_id = r.id
            WHERE (m.sender_id = ? AND m.receiver_id = ?)
               OR (m.sender_id = ? AND m.receiver_id = ?)
            ORDER BY m.sent_at ASC
        ");
        $stmt->execute([$user_id, $partner_id, $partner_id, $user_id]);
    } else {
        // تحميل جميع رسائل المستخدم
        $stmt = $pdo->prepare("
            SELECT m.*,
                   s.name as sender_name,
                   r.name as receiver_name
            FROM messages m
            LEFT JOIN users s ON m.sender_id = s.id
            LEFT JOIN users r ON m.receiver_id = r.id
            WHERE m.sender_id = ? OR m.receiver_id = ?
            ORDER BY m.sent_at DESC
        ");
        $stmt->execute([$user_id, $user_id]);
    }
    
    $messages = $stmt->fetchAll();
    
    // تحويل البيانات للتوافق مع الواجهة
    $formatted_messages = [];
    foreach ($messages as $message) {
        $formatted_messages[] = [
            'id' => $message['id'],
            'senderId' => $message['sender_id'],
            'receiverId' => $message['receiver_id'],
            'content' => $message['message'], // استخدام 'message' بدلاً من 'content'
            'attachments' => empty($message['attachments']) ? [] : json_decode($message['attachments'], true),
            'read' => (bool)$message['is_read'],
            'timestamp' => $message['sent_at'] ?? date('Y-m-d H:i:s'), // استخدام 'sent_at'
            'senderName' => $message['sender_name'],
            'receiverName' => $message['receiver_name']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'messages' => $formatted_messages
    ], JSON_UNESCAPED_UNICODE);
}

// إرسال رسالة
function sendMessage($pdo, $input) {
    $sender_id = $input['sender_id'] ?? '';
    $receiver_id = $input['receiver_id'] ?? '';
    $content = $input['content'] ?? '';
    $attachments = $input['attachments'] ?? [];
    
    if (!$sender_id || !$receiver_id) {
        throw new Exception('معرف المرسل والمستقبل مطلوبان');
    }
    
    if (empty($content) && empty($attachments)) {
        throw new Exception('محتوى الرسالة أو المرفقات مطلوبة');
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO messages (sender_id, receiver_id, message, attachments)
        VALUES (?, ?, ?, ?)
    ");

    $attachments_json = empty($attachments) ? null : json_encode($attachments, JSON_UNESCAPED_UNICODE);

    $stmt->execute([
        $sender_id,
        $receiver_id,
        $content, // سيتم حفظه في عمود 'message'
        $attachments_json
    ]);
    
    $message_id = $pdo->lastInsertId();
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إرسال الرسالة بنجاح',
        'message_id' => $message_id
    ], JSON_UNESCAPED_UNICODE);
}

// تحديد الرسائل كمقروءة
function markMessagesAsRead($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $partner_id = $input['partner_id'] ?? '';
    
    if (!$user_id || !$partner_id) {
        throw new Exception('معرف المستخدم والشريك مطلوبان');
    }
    
    $stmt = $pdo->prepare("
        UPDATE messages
        SET is_read = TRUE
        WHERE sender_id = ? AND receiver_id = ? AND is_read = FALSE
    ");
    
    $stmt->execute([$partner_id, $user_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديد الرسائل كمقروءة'
    ], JSON_UNESCAPED_UNICODE);
}

// حذف محادثة
function deleteConversation($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $partner_id = $input['partner_id'] ?? '';
    
    if (!$user_id || !$partner_id) {
        throw new Exception('معرف المستخدم والشريك مطلوبان');
    }
    
    $stmt = $pdo->prepare("
        DELETE FROM messages 
        WHERE (sender_id = ? AND receiver_id = ?) 
           OR (sender_id = ? AND receiver_id = ?)
    ");
    
    $stmt->execute([$user_id, $partner_id, $partner_id, $user_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف المحادثة بنجاح'
    ], JSON_UNESCAPED_UNICODE);
}

// جلب المحادثات
function getConversations($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    $stmt = $pdo->prepare("
        SELECT
            CASE
                WHEN m.sender_id = ? THEN m.receiver_id
                ELSE m.sender_id
            END as partner_id,
            CASE
                WHEN m.sender_id = ? THEN r.name
                ELSE s.name
            END as partner_name,
            m.message as last_message,
            m.sent_at as last_message_time,
            COUNT(CASE WHEN m.receiver_id = ? AND m.is_read = FALSE THEN 1 END) as unread_count
        FROM messages m
        LEFT JOIN users s ON m.sender_id = s.id
        LEFT JOIN users r ON m.receiver_id = r.id
        WHERE m.sender_id = ? OR m.receiver_id = ?
        GROUP BY partner_id, partner_name
        ORDER BY last_message_time DESC
    ");
    
    $stmt->execute([$user_id, $user_id, $user_id, $user_id, $user_id]);
    $conversations = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'conversations' => $conversations
    ], JSON_UNESCAPED_UNICODE);
}
?>
