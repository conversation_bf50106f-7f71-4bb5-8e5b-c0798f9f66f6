<?php
/**
 * تحديث ملفات API لاستخدام قاعدة البيانات الجديدة
 * Switch API files to use new database
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h2>تحديث ملفات API لقاعدة البيانات الجديدة</h2>";

// قائمة ملفات API التي تحتاج تحديث
$api_files = [
    'children-api.php',
    'medicines-api.php', 
    'messages-api.php',
    'monthly-planning-api.php',
    'user-management-api.php',
    'vaccination-api-simple.php',
    'vaccination-status-api.php',
    'vaccines-api.php',
    'user-api-simple.php'
];

$updated_files = [];
$failed_files = [];

foreach ($api_files as $file) {
    if (file_exists($file)) {
        try {
            // قراءة محتوى الملف
            $content = file_get_contents($file);
            
            // البحث عن السطر الذي يتضمن database-live.php
            if (strpos($content, "require_once 'config/database-live.php';") !== false) {
                // استبدال المرجع إلى قاعدة البيانات الجديدة
                $new_content = str_replace(
                    "require_once 'config/database-live.php';",
                    "require_once 'config/database-new.php';",
                    $content
                );
                
                // إنشاء نسخة احتياطية
                $backup_file = $file . '.backup.' . date('Y-m-d-H-i-s');
                file_put_contents($backup_file, $content);
                
                // كتابة المحتوى الجديد
                file_put_contents($file, $new_content);
                
                $updated_files[] = [
                    'file' => $file,
                    'backup' => $backup_file,
                    'status' => 'تم التحديث بنجاح'
                ];
                
            } else {
                $failed_files[] = [
                    'file' => $file,
                    'reason' => 'لم يتم العثور على مرجع database-live.php'
                ];
            }
            
        } catch (Exception $e) {
            $failed_files[] = [
                'file' => $file,
                'reason' => 'خطأ: ' . $e->getMessage()
            ];
        }
    } else {
        $failed_files[] = [
            'file' => $file,
            'reason' => 'الملف غير موجود'
        ];
    }
}

// عرض النتائج
echo "<h3>الملفات التي تم تحديثها بنجاح:</h3>";
if (!empty($updated_files)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الملف</th><th>النسخة الاحتياطية</th><th>الحالة</th></tr>";
    foreach ($updated_files as $file_info) {
        echo "<tr>";
        echo "<td>" . $file_info['file'] . "</td>";
        echo "<td>" . $file_info['backup'] . "</td>";
        echo "<td style='color: green;'>" . $file_info['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لم يتم تحديث أي ملف.</p>";
}

echo "<h3>الملفات التي فشل تحديثها:</h3>";
if (!empty($failed_files)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الملف</th><th>السبب</th></tr>";
    foreach ($failed_files as $file_info) {
        echo "<tr>";
        echo "<td>" . $file_info['file'] . "</td>";
        echo "<td style='color: red;'>" . $file_info['reason'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: green;'>تم تحديث جميع الملفات بنجاح!</p>";
}

echo "<hr>";
echo "<h3>ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>تم إنشاء نسخ احتياطية من جميع الملفات المحدثة</li>";
echo "<li>يمكنك استعادة النسخ الاحتياطية في حالة وجود مشاكل</li>";
echo "<li>تأكد من اختبار جميع وظائف API بعد التحديث</li>";
echo "<li>إذا واجهت مشاكل، يمكنك استعادة الملفات الأصلية من النسخ الاحتياطية</li>";
echo "</ul>";

echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li><a href='test-database-connection.php'>اختبار الاتصال بقاعدة البيانات الجديدة</a></li>";
echo "<li>اختبار وظائف API المختلفة</li>";
echo "<li>حذف النسخ الاحتياطية بعد التأكد من عمل كل شيء بشكل صحيح</li>";
echo "</ol>";
?>
